import { ColumnDef, VisibilityState } from "@tanstack/react-table";
import { FormComponentType } from "./form";
import { UserRole } from "./auth";
import { ForwardRefExoticComponent, RefAttributes, ComponentType } from "react";
import { LucideProps } from "lucide-react";

/**
 * Configuration for URL parameters in dynamic pages
 */
export interface ParameterConfig {
  required?: string[];
  optional?: string[];
  pathPattern?: string;
  validation?: Record<string, (value: string) => boolean>;
}

/**
 * Props for custom dialog components
 */
export interface CustomDialogProps {
  onSuccess?: (data?: any) => void;
  onCancel?: () => void;
  contextData?: Record<string, any>;
  entityId?: string;
  initialData?: Record<string, any>;
}

/**
 * Configuration for custom dialog components
 */
export interface CustomDialogConfig {
  component: ComponentType<CustomDialogProps>;
  props?: Record<string, any>;
}

/**
 * Configuration for custom dialogs in entity operations
 */
export interface CustomDialogsConfig {
  create?: CustomDialogConfig;
  edit?: CustomDialogConfig;
}

/**
 * Base configuration for any dynamic page
 */
export interface PageConfigBase {
  id: string;
  title: string;
  description?: string;
  entityName: string;
  permissions?: UserRole[];
  endpoints: {
    list: string;
    get: string;
    create: string;
    update: string;
    delete: string;
  }; // Required permissions to access this page
}

/**
 * Configuration for a list page
 */
export interface ListPageConfig extends PageConfigBase {
  type: "list";
  columns: ColumnConfig[];
  actions: ActionConfig[];
  defaultPageSize?: number;
  defaultSorting?: { id: string; desc: boolean }[];
  enableGlobalFilter?: boolean;
  enableColumnFilters?: boolean;
  enablePinning?: boolean;
  defaultPinnedColumns?: Record<string, number>;
  createFormConfig?: FormPageConfig;
  defaultVisibilityState?: VisibilityState;
  // Optional embedded form config for create/edit

  // New: Custom dialog configurations
  customDialogs?: CustomDialogsConfig;

  // New: URL parameter configuration
  parameterConfig?: ParameterConfig;

  // New: Context data transformation function
  contextTransform?: (params: Record<string, string>) => Record<string, any>;
}

/**
 * Configuration for a form page
 */
export interface FormPageConfig extends PageConfigBase {
  type: "form";
  fields: FieldConfig[];
  submitButtonText?: string;
  cancelButtonText?: string;
  successMessage?: string;
  errorMessage?: string;
  redirectAfterSubmit?: string;
  isMultiStep?: boolean;
}

/**
 * Configuration for a column in a list page
 */
export interface ColumnConfig {
  id: string;
  header: string;
  accessorKey: string;
  type:
    | "text"
    | "number"
    | "date"
    | "datetime"
    | "status"
    | "progress"
    | "custom"
    | "select"
    | "currency"
    | "percent";
  enableSorting?: boolean;
  enableColumnFilter?: boolean;
  enablePinning?: boolean;
  cell?: (value: any) => React.ReactNode;
  width?: number;
  formatOptions?: {
    showIcon?: boolean;
    showLabel?: boolean;
    size?: "sm" | "md" | "lg";
    variant?: string;
    animated?: boolean;
    showTime?: boolean;
  };
}

/**
 * Configuration for an action in a list page
 */
export interface ActionConfig {
  id: string;
  label: string;
  icon?:
    | string
    | ForwardRefExoticComponent<
        Omit<LucideProps, "ref"> & RefAttributes<SVGSVGElement>
      >;
  action?: "view" | "edit" | "delete" | "custom";
  handler?: (row: any) => Promise<void> | void;
  permissions?: UserRole[]; // Required permissions to see/use this action
  variant?:
    | "default"
    | "destructive"
    | "outline"
    | "secondary"
    | "ghost"
    | "link";
  size?: "default" | "sm" | "lg" | "icon";
  requireConfirmation?: boolean;
}

/**
 * Context object passed to dynamic functions
 */
export interface FieldContext {
  /** Current form values */
  formValues: Record<string, any>;
  /** Initial/existing data being edited */
  initialData?: Record<string, any>;
  /** API client for making requests */
  apiClient: {
    get: <T = any>(url: string) => Promise<{ data: T }>;
    post: <T = any>(url: string, data?: any) => Promise<{ data: T }>;
    put: <T = any>(url: string, data?: any) => Promise<{ data: T }>;
    delete: <T = any>(url: string) => Promise<{ data: T }>;
  };
  /** Get value of a specific field */
  getFieldValue: (fieldName: string) => any;
  /** Check if a field has a value */
  hasFieldValue: (fieldName: string) => boolean;
}

/**
 * Configuration for dynamic options that depend on other fields
 */
export interface DynamicOptionsConfig {
  /** Function to resolve options based on context */
  resolver: (
    context: FieldContext
  ) => Promise<{ label: string; value: string }[]>;
  /** Fields that this dynamic options depends on */
  dependsOn?: string[];
  /** Cache key generator for optimization */
  cacheKey?: (context: FieldContext) => string;
  /** Whether to clear options when dependencies are empty */
  clearOnEmptyDependency?: boolean;
  /** Default options to show when dependencies are not met */
  defaultOptions?: { label: string; value: string }[];
}

/**
 * Configuration for auto-fill functionality
 */
export interface AutoFillConfig {
  /** Function to resolve the auto-fill value */
  resolver: (context: FieldContext) => Promise<any>;
  /** Fields that trigger auto-fill when changed */
  triggers?: string[];
  /** Whether to auto-fill only on initial load */
  onlyOnInitialLoad?: boolean;
  /** Transform function for the resolved value */
  transform?: (value: any, context: FieldContext) => any;
  /** Whether to create options from the resolved value (for select fields) */
  createOptions?: boolean;
  /** Function to format options from resolved value */
  optionsFormatter?: (
    value: any,
    context: FieldContext
  ) => { label: string; value: string }[];
}

/**
 * Configuration for a field in a form page
 */
export interface FieldConfig {
  id: string;
  name: string;
  label: string;
  type: FormComponentType;
  required?: boolean;
  placeholder?: string;
  defaultValue?: any;
  options?: { label: string; value: string }[];

  /** Legacy dynamic options - use dynamicOptionsConfig for new implementations */
  dynamicOptions?: () => Promise<{ label: string; value: string }[]>;
  /** Enhanced dynamic options with dependency support */
  dynamicOptionsConfig?: DynamicOptionsConfig;

  min?: number | string;
  max?: number | string;
  step?: number;
  unit?: string;
  validations?: FieldValidation[];
  conditionalRendering?: FieldConditionalRendering;
  parentId?: string;
  disabled?: boolean;

  /** Legacy auto-fill - use autoFillConfig for new implementations */
  autoFill?: boolean;
  autoFillFn?: () => Promise<any>;
  /** Enhanced auto-fill with dependency support */
  autoFillConfig?: AutoFillConfig;

  // For grouping fields in sections or steps
}

/**
 * Configuration for field validation
 */
export interface FieldValidation {
  rule:
    | "required"
    | "min"
    | "max"
    | "minLength"
    | "maxLength"
    | "pattern"
    | "email"
    | "url";
  value?: any;
  message?: string;
}

/**
 * Configuration for conditional rendering of fields
 */
export interface FieldConditionalRendering {
  field: string;
  operator:
    | "equals"
    | "notEquals"
    | "contains"
    | "greaterThan"
    | "lessThan"
    | "empty"
    | "notEmpty";
  value: any;
}

/**
 * Unified entity configuration that combines list and form configurations
 */
export interface EntityConfig extends PageConfigBase {
  fields: FieldConfig[];
  listConfig: {
    columns: ColumnConfig[];
    actions: ActionConfig[];
    defaultPageSize?: number;
    defaultSorting?: { id: string; desc: boolean }[];
    enableGlobalFilter?: boolean;
    enableColumnFilters?: boolean;
    enablePinning?: boolean;
    defaultPinnedColumns?: Record<string, number>;
    defaultVisibilityState?: VisibilityState;
  };
  formConfig: {
    submitButtonText?: string;
    cancelButtonText?: string;
    successMessage?: string;
    errorMessage?: string;
    redirectAfterSubmit?: string;
    isMultiStep?: boolean;
  };

  // New: URL parameter configuration
  parameterConfig?: ParameterConfig;

  // New: Custom dialog configurations
  customDialogs?: CustomDialogsConfig;

  // New: Context data transformation
  contextTransform?: (params: Record<string, string>) => Record<string, any>;
}

/**
 * Union type for all page configurations
 */
export type PageConfig = ListPageConfig | FormPageConfig;

/**
 * Helper function to convert ColumnConfig[] to ColumnDef[]
 */
export function convertToColumnDefs<T>(
  columns: ColumnConfig[]
): ColumnDef<T, any>[] {
  return columns.map((column) => {
    // Create the column definition with accessor function
    const columnDef = {
      id: column.id,
      header: column.header,
      enableSorting: column.enableSorting ?? true,
      enableColumnFilter: column.enableColumnFilter ?? true,
      cell: column.cell,
      meta: {
        enablePinning: column.enablePinning ?? false,
        width: column.width,
      },
      // Set the accessor function based on accessorKey or id
      accessorFn: (row: any) => row[column.accessorKey || column.id],
    } as ColumnDef<T, any>;

    return columnDef;
  });
}

/**
 * Helper function to convert FieldConfig[] to FormComponent[]
 */
export async function convertToFormComponents(
  fields: FieldConfig[],
  initialData?: Record<string, any>,
  formValues?: Record<string, any>
): Promise<any[]> {
  // Import the field resolver functions
  const { createFieldContext, resolveAutoFill, resolveDynamicOptions } =
    await import("@/lib/utils/field-resolver");

  // Create context for resolvers
  const context = createFieldContext(formValues || {}, initialData, fields);

  const componentFields = await Promise.all(
    fields.map(async (field) => {
      let fieldOptions = field.options;
      let fieldDefaultValue = field.defaultValue;

      // Handle auto-fill (both legacy and new system)
      if (field.autoFill || field.autoFillConfig) {
        try {
          const autoFillResult = await resolveAutoFill(field, context);
          console.log("🚀 ~ fields.map ~ autoFillResult:", autoFillResult);
          if (autoFillResult.value !== null) {
            fieldDefaultValue = autoFillResult.value;
          }
          if (autoFillResult.options) {
            fieldOptions = autoFillResult.options;
          }
        } catch (error) {
          console.error(
            `Error resolving auto-fill for field ${field.id}:`,
            error
          );
        }
      }

      // Handle dynamic options (both legacy and new system)
      if (field.dynamicOptions || field.dynamicOptionsConfig) {
        try {
          fieldOptions = await resolveDynamicOptions(field, context);
          console.log("🚀 ~ fields.map ~ fieldOptions:", fieldOptions);
        } catch (error) {
          console.error(
            `Error resolving dynamic options for field ${field.id}:`,
            error
          );
          fieldOptions = field.options || [];
        }
      }

      return {
        id: field.id,
        type: field.type,
        label: field.label,
        name: field.name,
        required: field.required ?? false,
        placeholder: field.placeholder,
        defaultValue: fieldDefaultValue,
        options: fieldOptions,
        min: field.min,
        max: field.max,
        step: field.step,
        unit: field.unit,
        validations: field.validations,
        conditionalRendering: field.conditionalRendering,
        parentId: field.parentId,
        disabled: field.disabled,
        // Pass through the enhanced configurations for runtime use
        dynamicOptionsConfig: field.dynamicOptionsConfig,
        autoFillConfig: field.autoFillConfig,
      };
    })
  );

  return componentFields;
}
