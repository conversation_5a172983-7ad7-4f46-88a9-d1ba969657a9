import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import {
  PageConfig,
  ListPageConfig,
  FormPageConfig,
} from "@/lib/types/page-config";
import EnhancedDynamicListPage from "./EnhancedDynamicListPage";
import DynamicFormPage from "./DynamicFormPage";
import { useAuth } from "@/contexts/AuthContext";
import { Badge } from "@/components/ui/badge";
import CustomDialogWrapper from "./CustomDialogWrapper";
import { useEntityParams } from "@/hooks/useEntityParams";
import {
  ParameterErrorDisplay,
  shouldShowParameterErrors
} from "@/lib/utils/dynamic-page-utils";

interface DynamicPageProps {
  config: PageConfig;
  entityId?: string;
  dynamicPath?: string;
  contextData?: Record<string, any>;
}

export const DynamicPage = React.memo(function DynamicPage({
  config,
  entityId,
  dynamicPath = "",
  contextData = {},
}: DynamicPageProps) {
  const { hasPermission } = useAuth();
  const navigate = useNavigate();
  const [showForm, setShowForm] = useState(
    !!entityId || config.type === "form"
  );
  const [refetch, setRefetch] = useState(false);
  const [currentEntityId, setCurrentEntityId] = useState<string | null>(
    entityId ?? null
  );

  // Extract URL parameters if this is a list page with parameter config
  const paramResult = useEntityParams(
    config.type === "list" ? (config as ListPageConfig).parameterConfig : undefined
  );

  // Merge provided context data with extracted parameters
  const mergedContextData = {
    ...paramResult.contextData,
    ...contextData,
  };

  // Use dynamic path from parameters if not explicitly provided
  const effectiveDynamicPath = dynamicPath || paramResult.dynamicPath;

  const hasRequiredPermissions =
    !config.permissions ||
    config.permissions.every((permission) => hasPermission(permission as any));

  // Check for parameter validation errors
  const showParameterErrors = shouldShowParameterErrors(
    paramResult,
    config as ListPageConfig
  );

  if (!hasRequiredPermissions) {
    return (
      <div className="flex flex-col items-center justify-center p-8">
        <h2 className="text-2xl font-bold mb-4">Access Denied</h2>
        <p className="text-muted-foreground mb-6">
          You don't have permission to access this page.
        </p>
        <Badge variant="outline">Insufficient Permissions</Badge>
      </div>
    );
  }

  // Show parameter validation errors if any
  if (showParameterErrors) {
    return (
      <ParameterErrorDisplay
        errors={paramResult.errors}
        onRetry={() => window.location.reload()}
      />
    );
  }

  if (config.type === "form") {
    return (
      <DynamicFormPage
        config={config as FormPageConfig}
        entityId={currentEntityId ?? undefined}
      />
    );
  }

  return (
    <>
      <EnhancedDynamicListPage
        config={config as ListPageConfig}
        refetch={refetch}
        dynamicPath={effectiveDynamicPath}
        contextData={mergedContextData}
        onRefetchComplete={() => setRefetch(false)}
        onCreateNew={() => {
          if (config.entityName === "Form") {
            navigate("/forms/new");
            return;
          }
          setCurrentEntityId(null);
          setShowForm(true);
        }}
        onView={(id) => {
          navigate(`/${config.id}/${config.entityName.toLowerCase()}/${id}`);
        }}
        onEdit={(id) => {
          if (config.entityName === "Form") {
            navigate(`/${config.id}/${config.entityName.toLowerCase()}/${id}`);

            return;
          }
          setCurrentEntityId(id);
          setShowForm(true);
        }}
      />

      {!currentEntityId && (
        (config as ListPageConfig).createFormConfig ||
        (config as ListPageConfig).customDialogs?.create
      ) && (
          <CustomDialogWrapper
            open={showForm}
            onOpenChange={setShowForm}
            mode="create"
            customDialogs={(config as ListPageConfig).customDialogs}
            fallbackFormConfig={(config as ListPageConfig).createFormConfig}
            contextData={mergedContextData}
            onSuccess={() => {
              setShowForm(false);
              setRefetch(true);
            }}
            onCancel={() => setShowForm(false)}
            entityName={config.entityName}
          />
        )}

      {/* Edit Entity Dialog */}
      {currentEntityId && (
        <CustomDialogWrapper
          open={showForm}
          onOpenChange={setShowForm}
          mode="edit"
          customDialogs={(config as ListPageConfig).customDialogs}
          fallbackFormConfig={(config as ListPageConfig).createFormConfig}
          entityId={currentEntityId}
          contextData={mergedContextData}
          onSuccess={() => {
            setShowForm(false);
            setRefetch(true);
          }}
          onCancel={() => setShowForm(false)}
          entityName={config.entityName}
        />
      )}
    </>
  );
});

export default DynamicPage;
