import { SortingState } from "@tanstack/react-table";
import { TableFilter, TableData, PaginationState } from "./table-service";
import { apiClient } from "../api/api-client";
import {GetAllResponses} from "../types/api";

// In-memory storage for entities
const entityStorage: Record<string, Record<string, any>[]> = {};

/**
 * Service for handling entity data operations
 */
export const EntityService = {
  /**
   * Get all entities of a specific type with pagination, sorting, and filtering
   */

  buildParams(
    filter: {
      globalFilter?: string;
      columnFilters?: { id: string; value: string }[];
    },
    sorting?: { id: string; desc: boolean }[],
    pagination?: { pageIndex: number; pageSize: number }
  ): Record<string, string> {
    const params: Record<string, string> = {};

    // Global search term
    if (filter.globalFilter) {
      params["searchTerm"] = filter.globalFilter;
    }

    // Column-specific filters
    if (filter.columnFilters) {
      filter.columnFilters.forEach(({ id, value }) => {
        params[id] = value;
      });
    }

    // Sorting (as JSON-encoded string)
    if (sorting && sorting.length > 0) {
      const sortingParams = sorting.map((s) => s.id).join("");
      const sortingOrders = sorting
        .map((s) => (s.desc ? "desc" : "asc"))
        .join("");
      params["sort"] = `${sortingParams},${sortingOrders}`;
      // You can adjust this format if backend expects more
    }

    // Pagination
    if (pagination) {
      params["page"] = pagination.pageIndex.toString(); // Assuming backend pages start at 1
      params["size"] = pagination.pageSize.toString();
    }

    return params;
  },

  getEntities: async <T extends Record<string, any>>(
    entityName: string,
    endpoint: string,
    pagination: PaginationState,
    sorting: SortingState,
    filter: TableFilter
  ): Promise<TableData<T>> => {
    console.log("🚀 ~ entityName:", entityName);
    try {
        const params = EntityService.buildParams(filter, sorting, pagination);
        const response = await apiClient.get<GetAllResponses<T>>(endpoint, {
          params,
        });
        return {
          data: response.data.content,
          pageCount: response.data.page.totalPages,
          totalCount: response.data.page.totalElements,
        };
    } catch (error) {
      console.error("Error fetching projects:", error);

      throw error;
    }
  },

  /**
   * Get a specific entity by ID
   */
  getEntityById: async <T extends Record<string, any>>(
    entityName: string,
    endpoint: string,
    id: string
  ): Promise<T | null> => {
    console.log("🚀 ~ entityName:", entityName);
    try {
      const response = await apiClient.get<T>(`${endpoint}/${id}`);
      return response.data;
    } catch (error) {
      console.error("Error fetching project:", error);
      return null;
    }
  },

  /**
   * Create a new entity
   */
  createEntity: async <T extends Record<string, any>>(
    entityName: string,
    endpoint: string,
    data: Omit<T, "id" | "createdAt" | "updatedAt">
  ): Promise<T> => {
    console.log("🚀 ~ entityName:", entityName);
    try {
      const response = await apiClient.post<T>(`${endpoint}`, data);
      return response.data;
    } catch (error) {
      console.log("🚀 ~ error:", error);
      throw error;
      // return error
    }
  },

  /**
   * Update an existing entity
   */
  updateEntity: async <T extends Record<string, any>>(
    entityName: string,
    endpoint: string,
    id: string,
    data: Partial<T>
  ): Promise<T | null> => {
    console.log("🚀 ~ entityName:", entityName);
    try {
      const response = await apiClient.put<T>(`${endpoint}/${id}`, data);
      return response.data;
    } catch (error) {
      console.error("Error updating entity via API:", error);
      throw error;
    }
  },

  /**
   * Delete an entity
   */
  deleteEntity: async (
    entityName: string,
    endpoint: string,
    id: string
  ): Promise<boolean> => {
    console.log("🚀 ~ deleteEntity: ~ entityName:", entityName);
    try {
      await apiClient.delete(`${endpoint}/${id}`);
      return true;
    } catch (error) {
      console.error("Error deleting entity via API, ", error);
      throw error;
    }
  },

  /**
   * Initialize entity storage with mock data
   */
  initializeEntityStorage: (
    entityName: string,
    mockData: Record<string, any>[]
  ): void => {
    entityStorage[entityName] = mockData;
  },
};
