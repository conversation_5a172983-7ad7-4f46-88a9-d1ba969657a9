import React from 'react';
import { DynamicPage } from '@/components/dynamic-page/DynamicPage';
import { 
  createParameterizedListPageConfig,
  createParameterConfig,
  parameterValidators 
} from '@/lib/config/entity-config-registry';
import { CustomDialogsConfig } from '@/lib/types/page-config';
import { CustomDocumentUploadDialog } from './CustomDocumentUploadDialog';

/**
 * Example: Project Documents List with URL Parameters
 * 
 * URL Pattern: /projects/:projectId/documents/:category?
 * 
 * This page demonstrates:
 * - URL parameter extraction (projectId is required, category is optional)
 * - Parameter validation (projectId must be UUID, category must be alphanumeric)
 * - Context data transformation (converting URL params to meaningful context)
 * - Custom dialog for document upload
 */

// Configure URL parameters
const paramConfig = createParameterConfig({
  required: ['projectId'],
  optional: ['category'],
  pathPattern: '/projects/:projectId/documents/:category?',
  validation: {
    projectId: parameterValidators.isUUID,
    category: parameterValidators.isAlphanumeric,
  }
});

// Configure custom dialogs
const customDialogs: CustomDialogsConfig = {
  create: {
    component: CustomDocumentUploadDialog,
    props: {
      allowedTypes: ['pdf', 'docx', 'txt', 'jpg', 'png'],
      maxFileSize: 20 * 1024 * 1024, // 20MB
      enableVersioning: true,
    }
  }
};

// Transform URL parameters to meaningful context data
const contextTransform = (params: Record<string, string>) => ({
  parentProjectId: params.projectId,
  documentCategory: params.category || 'general',
  breadcrumb: `Project ${params.projectId} / Documents${params.category ? ` / ${params.category}` : ''}`,
  filterByProject: true,
});

export default function ProjectDocumentsList() {
  // Create the enhanced configuration
  const config = createParameterizedListPageConfig(
    'Document',
    paramConfig,
    customDialogs,
    contextTransform
  );

  // Handle configuration not found
  if (!config) {
    return (
      <div className="flex flex-col items-center justify-center p-8">
        <h2 className="text-2xl font-bold mb-4 text-destructive">Configuration Error</h2>
        <p className="text-muted-foreground">
          Document entity configuration not found. Please check your entity registry.
        </p>
      </div>
    );
  }

  return <DynamicPage config={config} />;
}

/**
 * Usage in React Router:
 * 
 * <Route 
 *   path="/projects/:projectId/documents/:category?" 
 *   element={<ProjectDocumentsList />} 
 * />
 * 
 * Example URLs:
 * - /projects/123e4567-e89b-12d3-a456-426614174000/documents
 * - /projects/123e4567-e89b-12d3-a456-426614174000/documents/contracts
 * - /projects/123e4567-e89b-12d3-a456-426614174000/documents/reports
 */
