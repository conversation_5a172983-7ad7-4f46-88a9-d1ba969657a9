
import { DynamicPage } from '@/components/dynamic-page/DynamicPage';
import {
    createParameterizedListPageConfig,
    createParameterConfig,
    parameterValidators
} from '@/lib/config/entity-config-registry';
import { CustomDialogsConfig } from '@/lib/types/page-config';
import { ProjectDocumentUploaderWrapper } from '@/components/documents/ProjectDocumentUploaderWrapper';

/**
 * Documents Page with URL Parameters and Custom Upload Dialog
 *
 * URL Pattern: /projects/:projectId/documents
 *
 * Features:
 * - Extracts projectId from URL parameters
 * - Filters documents by project using API query parameter: /documents?projectRef=projectId
 * - Uses custom ProjectDocumentUploader component for creating documents
 * - Only shows delete action in data table
 */

// Configure URL parameters
const paramConfig = createParameterConfig({
    required: ['projectId'],
    pathPattern: '/projects/:projectId/documents',

});

// Configure custom dialogs
const customDialogs: CustomDialogsConfig = {
    create: {
        component: ProjectDocumentUploaderWrapper,
        props: {
            // Additional props can be passed here if needed
        }
    }
    // Note: No edit dialog configured - documents are typically not edited, only replaced
};

// Transform URL parameters to context data
const contextTransform = (params: Record<string, string>) => ({
    parentProjectId: params.projectId,
    projectRef: params.projectId,
    breadcrumb: `Project ${params.projectId} / Documents`,
    // This will be used to filter the API call: /documents?projectRef=projectId
    filterParams: {
        projectRef: params.projectId,
    }
});

export default function DocumentsPage() {
    // Create the enhanced configuration
    const config = createParameterizedListPageConfig(
        'Document',
        paramConfig,
        customDialogs,
        contextTransform
    );

    // Handle configuration not found
    if (!config) {
        return (
            <div className="flex flex-col items-center justify-center p-8">
                <h2 className="text-2xl font-bold mb-4 text-destructive">Configuration Error</h2>
                <p className="text-muted-foreground">
                    Document entity configuration not found. Please check your entity registry.
                </p>
            </div>
        );
    }

    return <DynamicPage config={config} />;
}
