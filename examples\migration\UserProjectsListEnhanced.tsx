import React from 'react';
import { DynamicPage } from '@/components/dynamic-page/DynamicPage';
import { 
  createParameterizedListPageConfig,
  createParameterConfig,
  parameterValidators 
} from '@/lib/config/entity-config-registry';

/**
 * Enhanced User Projects List
 * 
 * This example shows how to migrate from a basic DynamicPage
 * to one that uses URL parameters for filtering.
 * 
 * URL Pattern: /users/:userId/projects
 * 
 * BEFORE (Basic DynamicPage):
 * - Shows all projects
 * - No URL parameter support
 * - Generic create dialog
 * 
 * AFTER (Enhanced DynamicPage):
 * - Shows projects filtered by user ID
 * - URL parameter extraction and validation
 * - Context data passed to forms
 */

// Configure URL parameters
const paramConfig = createParameterConfig({
  required: ['userId'],
  pathPattern: '/users/:userId/projects',
  validation: {
    userId: parameterValidators.isUUID,
  }
});

// Transform URL parameters to context data
const contextTransform = (params: Record<string, string>) => ({
  ownerId: params.userId,
  ownerType: 'user',
  breadcrumb: `User ${params.userId} / Projects`,
  defaultValues: {
    // Pre-fill form fields with context data
    ownerId: params.userId,
    status: 'active',
  }
});

export default function UserProjectsListEnhanced() {
  // Create enhanced configuration with URL parameters
  const config = createParameterizedListPageConfig(
    'Project',
    paramConfig,
    undefined, // No custom dialogs in this example
    contextTransform
  );

  if (!config) {
    return (
      <div className="flex flex-col items-center justify-center p-8">
        <h2 className="text-2xl font-bold mb-4 text-destructive">Configuration Error</h2>
        <p className="text-muted-foreground">
          Project entity configuration not found.
        </p>
      </div>
    );
  }

  return <DynamicPage config={config} />;
}

/**
 * MIGRATION COMPARISON:
 * 
 * === BEFORE (Basic) ===
 * 
 * export default function UserProjectsList() {
 *   const [config] = useState(() => createListPageConfig("Project"));
 *   return <DynamicPage config={config} />;
 * }
 * 
 * === AFTER (Enhanced) ===
 * 
 * export default function UserProjectsListEnhanced() {
 *   const paramConfig = createParameterConfig({
 *     required: ['userId'],
 *     validation: { userId: parameterValidators.isUUID }
 *   });
 *   
 *   const config = createParameterizedListPageConfig(
 *     'Project',
 *     paramConfig,
 *     undefined,
 *     (params) => ({ ownerId: params.userId })
 *   );
 * 
 *   return config ? <DynamicPage config={config} /> : null;
 * }
 * 
 * === BENEFITS ===
 * 
 * 1. URL Parameters: Automatically extracts userId from URL
 * 2. API Filtering: Appends /user/{userId} to API endpoints
 * 3. Context Data: Passes ownerId to create/edit forms
 * 4. Validation: Ensures userId is a valid UUID
 * 5. Error Handling: Shows validation errors for invalid URLs
 * 
 * === ROUTING ===
 * 
 * <Route 
 *   path="/users/:userId/projects" 
 *   element={<UserProjectsListEnhanced />} 
 * />
 * 
 * Example URLs:
 * ✅ /users/123e4567-e89b-12d3-a456-************/projects
 * ❌ /users/invalid-id/projects (validation error)
 * ❌ /users//projects (missing required parameter)
 */
