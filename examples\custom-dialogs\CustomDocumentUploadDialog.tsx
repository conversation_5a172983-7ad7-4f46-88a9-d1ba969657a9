import React, { useState, useCallback } from 'react';
import { CustomDialogProps } from '@/lib/types/page-config';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Upload, FileText, AlertCircle } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

/**
 * Custom dialog props extending the base CustomDialogProps
 */
interface CustomDocumentUploadProps extends CustomDialogProps {
  allowedTypes?: string[];
  maxFileSize?: number;
  enableVersioning?: boolean;
}

/**
 * Custom Document Upload Dialog
 * 
 * This example demonstrates:
 * - Custom dialog component implementation
 * - File upload with validation
 * - Progress tracking
 * - Error handling
 * - Context data usage
 * - Success/cancel callbacks
 */
export function CustomDocumentUploadDialog({
  onSuccess,
  onCancel,
  contextData,
  allowedTypes = ['pdf', 'docx', 'txt'],
  maxFileSize = 10 * 1024 * 1024, // 10MB default
  enableVersioning = false,
}: CustomDocumentUploadProps) {
  const { toast } = useToast();
  
  // Form state
  const [file, setFile] = useState<File | null>(null);
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [category, setCategory] = useState(contextData?.documentCategory || 'general');
  const [version, setVersion] = useState('1.0');
  
  // Upload state
  const [uploading, setUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [error, setError] = useState<string | null>(null);

  // File validation
  const validateFile = useCallback((selectedFile: File): string | null => {
    // Check file type
    const fileExtension = selectedFile.name.split('.').pop()?.toLowerCase();
    if (!fileExtension || !allowedTypes.includes(fileExtension)) {
      return `File type not allowed. Allowed types: ${allowedTypes.join(', ')}`;
    }

    // Check file size
    if (selectedFile.size > maxFileSize) {
      const maxSizeMB = Math.round(maxFileSize / (1024 * 1024));
      return `File size too large. Maximum size: ${maxSizeMB}MB`;
    }

    return null;
  }, [allowedTypes, maxFileSize]);

  // Handle file selection
  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = event.target.files?.[0];
    setError(null);

    if (!selectedFile) {
      setFile(null);
      return;
    }

    const validationError = validateFile(selectedFile);
    if (validationError) {
      setError(validationError);
      setFile(null);
      return;
    }

    setFile(selectedFile);
    
    // Auto-fill title from filename if not set
    if (!title) {
      const nameWithoutExtension = selectedFile.name.replace(/\.[^/.]+$/, '');
      setTitle(nameWithoutExtension);
    }
  };

  // Simulate upload with progress
  const simulateUpload = async (): Promise<any> => {
    return new Promise((resolve, reject) => {
      let progress = 0;
      const interval = setInterval(() => {
        progress += Math.random() * 30;
        setUploadProgress(Math.min(progress, 95));
        
        if (progress >= 95) {
          clearInterval(interval);
          // Simulate final processing
          setTimeout(() => {
            setUploadProgress(100);
            resolve({
              id: `doc_${Date.now()}`,
              title,
              filename: file?.name,
              size: file?.size,
              category,
              version: enableVersioning ? version : undefined,
              projectId: contextData?.parentProjectId,
            });
          }, 500);
        }
      }, 200);
    });
  };

  // Handle upload
  const handleUpload = async () => {
    if (!file || !title.trim()) {
      setError('Please select a file and enter a title');
      return;
    }

    setUploading(true);
    setUploadProgress(0);
    setError(null);

    try {
      // In a real implementation, you would call your API here
      const result = await simulateUpload();
      
      toast({
        title: "Document uploaded successfully",
        description: `${title} has been uploaded to the project.`,
      });

      onSuccess?.(result);
    } catch (uploadError) {
      const errorMessage = uploadError instanceof Error 
        ? uploadError.message 
        : 'Upload failed. Please try again.';
      
      setError(errorMessage);
      
      toast({
        title: "Upload failed",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setUploading(false);
      setUploadProgress(0);
    }
  };

  // Handle cancel
  const handleCancel = () => {
    if (uploading) {
      // In a real implementation, you would cancel the upload request here
      setUploading(false);
      setUploadProgress(0);
    }
    onCancel?.();
  };

  return (
    <div className="space-y-6 p-1">
      {/* Context Information */}
      {contextData?.breadcrumb && (
        <div className="text-sm text-muted-foreground border-b pb-2">
          {contextData.breadcrumb}
        </div>
      )}

      {/* Error Display */}
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* File Upload */}
      <div className="space-y-2">
        <Label htmlFor="file-upload">Select Document</Label>
        <div className="flex items-center space-x-2">
          <Input
            id="file-upload"
            type="file"
            accept={allowedTypes.map(type => `.${type}`).join(',')}
            onChange={handleFileChange}
            disabled={uploading}
            className="flex-1"
          />
          <Upload className="h-4 w-4 text-muted-foreground" />
        </div>
        <p className="text-xs text-muted-foreground">
          Allowed types: {allowedTypes.join(', ')} • Max size: {Math.round(maxFileSize / (1024 * 1024))}MB
        </p>
      </div>

      {/* File Info */}
      {file && (
        <div className="flex items-center space-x-2 p-2 bg-muted rounded">
          <FileText className="h-4 w-4" />
          <span className="text-sm">{file.name}</span>
          <span className="text-xs text-muted-foreground">
            ({Math.round(file.size / 1024)} KB)
          </span>
        </div>
      )}

      {/* Document Details */}
      <div className="grid grid-cols-1 gap-4">
        <div className="space-y-2">
          <Label htmlFor="title">Title *</Label>
          <Input
            id="title"
            value={title}
            onChange={(e) => setTitle(e.target.value)}
            placeholder="Enter document title"
            disabled={uploading}
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="description">Description</Label>
          <Textarea
            id="description"
            value={description}
            onChange={(e) => setDescription(e.target.value)}
            placeholder="Enter document description (optional)"
            disabled={uploading}
            rows={3}
          />
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="category">Category</Label>
            <Select value={category} onValueChange={setCategory} disabled={uploading}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="general">General</SelectItem>
                <SelectItem value="contracts">Contracts</SelectItem>
                <SelectItem value="reports">Reports</SelectItem>
                <SelectItem value="specifications">Specifications</SelectItem>
                <SelectItem value="drawings">Drawings</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {enableVersioning && (
            <div className="space-y-2">
              <Label htmlFor="version">Version</Label>
              <Input
                id="version"
                value={version}
                onChange={(e) => setVersion(e.target.value)}
                placeholder="1.0"
                disabled={uploading}
              />
            </div>
          )}
        </div>
      </div>

      {/* Upload Progress */}
      {uploading && (
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span>Uploading...</span>
            <span>{Math.round(uploadProgress)}%</span>
          </div>
          <Progress value={uploadProgress} />
        </div>
      )}

      {/* Actions */}
      <div className="flex justify-end space-x-2 pt-4 border-t">
        <Button
          variant="outline"
          onClick={handleCancel}
          disabled={uploading}
        >
          Cancel
        </Button>
        <Button
          onClick={handleUpload}
          disabled={!file || !title.trim() || uploading}
        >
          {uploading ? 'Uploading...' : 'Upload Document'}
        </Button>
      </div>
    </div>
  );
}
